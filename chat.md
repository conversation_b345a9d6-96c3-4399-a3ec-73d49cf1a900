#对话控制系统
关联系统及其相互关系：
ideal：提供消息内容。ideal是一个即使通讯平台，交易对手在ideal进行询价
对话控制系统：根据ideal的不同消息内容，调用量化系统的不同接口。
量化交易系统：根据收到的询价要素进行报价。


对话控制包含了债券和回购业务，希望做到可配置化，未来能够快速新增其他业务品种

债券报价机器人流程
```mermaid
flowchart TB
  A(("客户点击小兴兴现券，输入任意文字"))
  B["接入询价机器人 小兴兴"]
  A --> B
  C{"是否在小兴兴服务时间？"}
  B --> C
  C -- "否" --> N1["文本展示：现在不在交易时间，小兴兴暂时无法为您服务"]
  C -- "是" --> D["文本展示：嗨！小兴兴恭候多时！目前我可以为您提供如下服务：<br/>
  1、现券T+1二级询价(服务时间xxxx)<br/>
  2、质押式回购溶出（服务时间xxxx）<br/>
  0、退出<br/>
  每次询价只能询一笔哦
  "]
  D -- "现券暂停服务" --> N2["文本展示：小兴兴现券目前暂停服务，请稍候<br/>请选择相应数字，如需退出，请回复0"]
  D -- "现券开启服务" --> N3["文本展示：小兴兴现券已开始服务，您可以接着询价"]
  D --> M1["文本展示：请选择相应数字，如需退出，请回复0."]
  M1 --> G{"识别到用户选择或指令？"}
  G -- "识别到0 或超5分钟无应答" --> End1["文本展示：询价结束，欢迎下次光临！"] --> status1["保存成交状态标签为“未选择询价场景"]
  G -- "未识别" --> H{"是否已连续三次未识别？"}
  H -- "是" --> End2["文本展示：询价结束，欢迎下次光临！"] --> status2["保持成交状态标签为”询价场景选择失败"]
  H -- "否" --> M1
  G -- "识别1" --> O["文本展示：请按如下格式发送询价<br/>
  各要素以空格分隔，例：bid 110209 1000w T+1<br/>
  温馨提示：1、方向、券码、量、交割方式为必须哦！<br/>
  2、目前支持询价品种：利率债<br/>
  3、支持交割方式：T+1"]
  O -- "客户输入询价要素" -->  I{"询价要素是否完整"}
  I -- "是" --> J{"询价量是否为1000万整数倍"}
  I -- "否" --> K
  J -- "否" --> K{连续输入错误是否达3次}
  K -- "是" --> k1["文本展示：询价要素多次输入错误，请您重新确认后再联系小兴兴哦<br/>
  本次询价结束，欢迎下次光临！"]
  K -- "否" --> k2["文本展示：您的询价要素不完整，还需补充：XXX、XXX（展示缺失要素）<br/>非1000万整数倍：请按1000万的整数倍询价"]
  J -- "是" --> k3{"询价要素是否错误"}
  k3 -- "是" --> K
  k3 -- "否" --> k4{"根据券码区分是存单还是现券"}
  k4 -- "现券" --> k5["进入现券询价流程"]
  k4 -- "存单" --> k6["进入存单询价流程"] --> cun1{"调量化交易系统询价请求接口查询债券信息及报价要素"}
  cun1 -- "接口异常" --> cun2["文本展示：很抱歉，小兴兴有点累了，请稍后再试"] --> status3["保存成交状态标签为报价失败，量化接口异常"]
  cun2 -- "接口正常" --> cun3{"量化是否拒绝询价"}
  cun3 -- "是" -->cun4["其他量化返回的建议回复"]-->status5["保存成交状态为询价要素确认失败"]
  cun3 -- "可报价额度为0" --> cun5["文本展示：量化返回的拒绝理由"] --> status5
  cun3 -- "券种无法报价" --> cun6["文本展示：当前券种无法报价，如有疑问，请联系xxx"] --> status5
  cun3 -- "券种不支持" --> cun7["文本展示：您的询价券种不支持，目前可办理的询价品种为利率债，请重新确认后再联系小兴兴"]--> status5
  cun3 -- "否" -->cun8["文本展示：报价查询中，请稍后"] --> cun9{"量化是否返回回价结果"}
  cun9 -- "等待量化报价一半时间为收到报价" --> cun10["文本展示：小兴兴查询报价中，请您耐心等待，若情况紧急，您可直接联系交易员xxx"]
  cun10 -- "超时未收到回价" --> cun11["文本展示：很抱歉，因系统原因暂时无法报价，请您联系交易员xxx ideal账号：xxx"]-->status6["保存成交状态标签为：报价失败，未获取到报价"]
  cun9 -- "否" -->cun12{"量化是否返回建议回复"} -- "是" --> cun13["如果没有拿到报价信息，直接展示量化系统返回的建议回复"]-->status7["保存成交状态标签为：报价失败，未获取到报价"]
  cun12 --"否（兜底）" --> cun14["文本展示：抱歉，现在有点忙，可以联系交易员xxx Ideal账号：xxx"]--> status7
  cun9 -- "其他情况拒绝（未收到拒绝原因）"--> cun15["文本展示：很抱歉，因系统原因暂时无法报价，请您联系交易员xxx ideal账号：xxx<br/>
  若无报价交易员信息，或不全，展示文本：很抱歉，因系统原因暂时无法报价，请您联系交易员xxx  ideal账号：xxx(值班交易员)"]
  cun9 -- "是" --> cun16{"回价要素是否完整，有效时间、价格、报价交易员等"} 
  cun16 -- "文本报价"--> cun17["文本展示：按标准格式回复报价<br/>
  例：ofr 110209 2.69 1000w +1 <br/>
  回复“done”同意，回复“ref”拒绝<br/>
  温馨提示：报价有效期未x秒钟，请在有效期内回复，若超时未回复，本次报价将自动失效。"]
  cun17-->cun18{"根据客户回复内容区分"} 
  cun18 -- "回执接口调用失败" --> cun19["提醒我方交易员，文本展示：<br/>
  成交：【量化回执调用失败-成交】【对手方】【交易信息】<br/>
  拒绝：【量化回执调用失败-失败】【对手方】【交易信息】<br/>
  超时未回复：【量化回执调用失败-成交】【对手方】【交易信息】<br/>
  三次回复未识别：【量化回执调用失败-成交】【对手方】【交易信息】"]
  cun18-->cun220["识别done或带有空格的done"]
  cun220-->cun221["文本展示：好的，您的这笔询价订单已确认，我行报价信息为xxx。<br/>
  注意：我行暂不支持我方请求报价，请按照一下内容选择交易方式：<br/>
  1、由我行向您【对手方机构】【对手方xxx】发起对话<br/>
  2、由您向我行交易员xxx发起RFQ<br/>
  3、由您向我方交易员xxx发起对话<br/>
  4、由您向我方柜台发起现券请求报价<br/>
  回复数字选择对应交易方式"]
  cun221 --> cun21{"根据用户输入进行区分"}
  cun21 -- "（量化回执调用失败）"-->cun22["文本展示：很抱歉，小兴兴有点累了"]
  cun21 --"识别1"-->cun223["文本展示：您选择了由我行发起对话。稍后由交易员xxx发起对话报价订单，请尽快在外汇交易中心本币交易系统确认"]-->cun23["保存成交状态标签为“已成交已确认交易方式为我方发起”，并返回量化系统(0105回执接口传递对应码值)"]
  cun21 --"识别2"-->cun24["文本展示:您选择了由自己发送RFO。发送RFO至我行交易XXXX(IDeal:XxxX成交要素:债券代码报价交易量交割方式，xxx出给xxx，xxx发送询价单至xxx"]-->cun25["保存成交状态标签为“已成交已确认交易方式为对方发起”，并返回量化系统(0105回执接口传递对应码值)"]
  cun21 --"识别3"-->cun26["文本展示:您选择了由自己发起对话。请发这对话至xxx交易员)X(IDeal账号:xxx
  成交要素:债券代码报价交易量交制方式，，xxx发送询价单至xxx."]-->cun27["保存成交状态标签为“已成交已确认交易方式为对方发起”，并返回量化系统(0105回执接口传递对应码值)"]
  cun21 --"识别4"-->cun28["文本展示:您选择了由自己发起柜台债现券请求报价。请通过银银平台发送RFQ。成交要素:债券代码报价交易量交割方式，xxx出给xxx，xXX柜台发送报价单。"]-->cun29["保存成交状态标签为“已成交已确认交易方式为对方发起”，并返回量化系统(0105回执接口传递对应码值)"]
  cun21 --"等待超过15分钟"-->cun30["文本展示：很抱歉，等待超时，请您尽快确认交易方式后直接联系交易员xxx(回价交易员)"]
  cun30 -->cun31["提醒兴业银行交易员xxx:X交易对手XxX交易员成交后未确认成交方式(把交易要素附请尽快与对方联系!"]-->cun32["保存成交状态标签为“已成交未确认交易方式，超时等待”，并返回量化系统(0105回执接口传递对应码值)"]
  cun21 --"未识别"-->cun33{"是否三次未识别"}
  cun33 --"是"-->cun34["文本展示：很抱歉，交易方式选择失败，请您尽快确认交易方式后直接联系交易员xxx"]
  cun34-->cun35["提醒兴业银行交易员xxx:X交易对手XxX交易员成交后未确认成交方式(把交易要素附请尽快与对方联系!"]-->cun36["保存成交状态标签为“已成交未确认交易方式，未识别”，并返回量化系统(0105回执接口传递对应码值)"]
  cun33--"否"-->cun37["文本展示：菜单选择错误，回复数字1、2、3、4选择对应交易方式"]
  cun18 -->cun38["识别ref或带有空格ref"]
  cun38-->cun39["文本展示：本次询价结束，欢迎下次光临！"]
  cun39 --> cun40["保存成交状态标签未”已报价未成交，对方拒绝，返回给量化0105回执接口"]
  cun18-->cun41["未识别"]
  cun41-->cun42["文本展示：回复done同意回复ref拒绝"]
  cun42-->cun43{"是否是第2次"}
  cun43--"是 未识别"-->cun44["文本展示：回复done同意回复ref拒绝。若对报价有异议请直接联系交易员"]
  cun44-->cun45["地三次：很抱歉，小兴兴没明白你的意思"]
  cun18 --> cun46["带有其他字符的done或ref"]
  cun46-->cun47["文本输入：请重新输入done或ref，注意不要再done或ref前后带有空格或其他字符"]
  cun47-->cun43
  cun18-->cun48["等待"]
  cun48-->cun49{"根据每笔报价有效时间进行判断（量化返回的）"}
  cun49-->cun50["等待时间超过有效时间一半：小兴兴还没收到您的回复，请尽快确认，回复done同意回复ref拒绝"]
  cun50-->cun51["继续等待至报价有效时间：很抱歉，等待超时，上述报价已失效"]
  cun51-->cun52["文本展示：本次询价结束，欢迎下次光临"]
```

贵金属业务询价流程
```mermaid
flowchart TD    
A(["客户点击小兴兴头像，输入任意文字"]) --> B["接入询价机器人"]    
B --> C{"判断是否在小兴兴服务时间"}    
C -- "否" --> D["文本展示：现在不在交易时间，小兴兴无法为您服务"]    
C -- "是" --> E["文本展示：嗨！小兴兴恭候多时！目前我可以为您提供如下服务：<br/>    
1、贵金属掉期（服务时间为xxxx-xxxx）服务时间量化系统端可修改<br/>    
0、退出<br/>    每次询价只能询一笔哦"]    
E --"贵金属开启服务"--> F["文本展示：小兴兴贵金属已开始服务，您可以接着询价"]    
E -- "贵金属暂停服务" --> G["文本展示：小兴兴贵金属目前暂停服务，请稍候。<br/>    
请选择相应数字，如需退出，请回复0"]    
E --> H["文本展示：请选择相应数字，如需退出，请回复0"]    
H --"识别0或超过5分钟无应答" --> I["文本展示：询价结束，欢迎下次光临"]
I-->J["保存成交状态标签为未选择询价场景"]    
H --"未识别"-->K{"是否三次未识别"}    
K --"否" -->H    
K --"是" --> M["文本展示：询价结束，欢迎下次光临"]
M-->N["保存成交状态标签为询价场景选择失败"]    
H--"识别1"-->O{"判断机构是否在白名单,白名单由量化系统提供"}    
O --"否" -->P["文本展示：目前无交易额度，小兴兴暂不支持报价"]
P-->Q["保存成交状态标签为“客户不在白名单，退出询价”"]    
O --"是" -->R["文本展示：<br/>
请提供您需要的标的、重量、方向、期限。<br/>    
重量最低为100KG，最高为1T。<br/>    
期限仅接受ON、TN、SN，非上述标准期限请输入近端<br/>    
计息日和远端计息日，输入格式为YYYY/MM/DDYYYY/MM/DD<br/>    
请依次输入询价要素（标的、重量、方向、期限）且用空格隔开。<br/>    
例1：AUX 100KG S/B TN<br/>    
例2：AUY 200KG B/S 2025/02/26-2025/03/05"]
R--"等待超过10分钟"-->z11["文本展示：询价要素输入超时。
<br/>本次询价结束，欢迎下次光临！"]
z11-->z12["保存成交状态标签为“询价要素确认失败”"]    
R --"客户输入询价要素" -->S{"询价要素是否完整"}   
S -- "否" --> gui1{"连续输入错误是否达到3次"}    
gui1 -- "是" --> gui3["文本展示：询价要素多次输入错误，请您重新确认后再联系小兴兴哦~
本次询价结束，欢迎下次光临！"]-->z13["保存成交状态标签为“询价要素确认失败”"]    
gui1 -- "否" --> z14["要素不完整：您的询价要素不完整，还需补充：XXX、XXX。<br/>
（展示缺失的要素）<br/>
重量小于100KG：重量最少为100KG，请重新输入重量。<br/>
重量大于1T：重量最高为1T，请重新输入重量。<br/> 
标的不存在：标的不存在，请输入支持标的AUX、AUY。<br/> 
期限格式不符合规范（不作标准期限校验）：期限输入错误，
标准期限输入格式可以参考：ON、TN、SN。非上述标准<br/> 
期限请输入近端计息日和远端计息日，输入格式为<br/>
YYYY/MM/DD-YYYY/MM/DD"] -->R    
S --"是" --> U{"重量是否大于等于100KG且小于等于1T"}
U--"是"-->V{"询价要素是否错误"}    
U --"否" -->gui1    
V --"是" -->gui1    
V --"否" -->gui4{"调量化询价请求接口查询信息及报价要素"}    
gui4 -- "接口异常" --> z111["文本展示：很抱歉，小兴兴有点累了，请稍后再试~"]-->z112["保存成交状态标签为“报价失败，量化接口异常”"]    
gui4 -- "接口正常" --> gui5{"量化是否拒绝询价"}    
gui5 --"是" -->z114["其他量化返回的建议回复"]-->z115["保存成交状态标签为“询价要素确认失败”"]    
gui5 --"否" -->z116["文本展示：报价查询中，请稍等。"]-->gui6{量化是否返回回价结果}    
gui6 -- "等待量化报价一半时间未收到报价"-->gui7["文本展示：小兴兴查询报价中，请您耐心等待，若情况较为紧急，您也可以直接联系交易员XXXX"]    
gui7--"超时未回价"-->z146["文本展示：很抱歉，因系统原因暂时无法报价，请您联系交易员XXX（IDeal账号：XXXX）。"]
gui7-->gui8["保存成交状态标签为“报价失败，未获取到报价”"]    
gui6 -- "等待量化报价一半时间未收到报价(兜底)"-->gui9["文本展示：小兴兴查询报价中，请您耐心等待。"]    
gui9--"超时未回价"-->z148["文本展示：很抱歉，因系统原因暂时无法报价。"]-->gui8    
gui6 --"否（拒绝报价）"-->gui10{"量化是否返回建议回复"}    
gui10--"是"-->z151["如果没拿到报价信息，直接展示量化系统返回的建议回复"]-->gui11["保存成交状态标签为“报价失败，未获取到报价”"]    
gui10--"否（兜底）"-->z150["文本展示：抱歉，现在有点忙，可以联系交易员XXX（IDeal账号：XXX）"]-->gui11    
gui6 -- "是" -->gui12{"回价要素是否完整"}    
gui12 --"无价格/无报价有效期/兜底" --> z153["文本展示：抱歉，现在有点忙，可以联系交易员XXX（IDeal账号：XXX）。若报价交易员信息不全，只有IDeal账号：抱歉，现在有点忙，可以联系交易员（IDeal账号：XXX）。<br/>
若报价交易员信息不全，只有姓名：抱歉，现在有点忙，可以联系交易员XXX。<br/>若无报价交易员信息，展示文本：抱歉，现在有点忙，可以联系<br/>
交易员XXX（IDeal账号：XXX）（值班交易员）。"] -->z154["保存成交状态标签为“报价失败，    
未获取到报价”或者“报价失败， 已过报价有效期”或者”“报价失败，无报价有效期”"]    
gui12 -- "是" --> gui13["文本展示：按标准格式回复报价<br/>
例：<br/>    1.08%<br/>    IBCN S/B AUY WITH BCHO 1000kg ,2025/2/20-2025/3/5 ,13D,677.7/677.964,26.4CTS<br/>
回复“done”同意，回复“ref”拒绝。<br/>   温馨提示：<br/>
报价有效期为2分钟（根据量化提供时间戳填写），请在有效期内回复，若超时未<br/>
回复，本次报价将自动失效。    "]    
gui13 -- "否" --> gui14{"根据客户回复内容区分"}    
gui14 -- "回执接口调用失败"-->x11["提醒我方交易员，文本展示：    
成交：【量化回执调用失败-成交】【对手方】【交易信息】    
拒绝：【量化回执调用失败-拒绝】【对手方】【交易信息】    
超时未回复：【量化回执调用失败-超时】【对手方】【交易信息】    
三次回复未识别：【【量化回执调用失败-未识别】【对手方】【交易信息】"]    
gui14 -->x12["识别“done”（需精准识别）"] -->gui15["文本展示：好的，交易意向已达成，请您尽快    向我行交易员XXX发起RFQ（IDeal账号：    XXXX），谢谢！    如有疑问，请您联系交易员XXX"]    
gui15 --> x13["返回给量化系统成交状态（0105回执接口传递对应码值）"]    
gui15-->guiR    
gui14 -->x14["识别“ref”（精准识别）"]-->gui16["文本展示：本次询价结束，欢迎下次光临！"]-->x18["返回给量化系统成交状态（0105回执接口传递对应码值）"]    
gui16 -->guiR    
gui14 -->x15["未识别（讨价还价）"] -->x19["文本展示：    
第一轮：回复“done”同意，回复“ref”拒绝。    
第二轮：回复“done”同意，回复“ref”拒绝。    
若对报价有异议，请直接联系交易员XXX（回价交易员）。 "]
x15-->gui17["第三轮：很抱歉，小兴兴没有明白您的意思，    
若对报价有异议，请直接联系交易员XXX（回价交易员）。本次询价结束，欢迎下次光临！"]
gui17-->x16["返回量化系统成交状态（0105回执接口传递对应码值）"]    
gui17-->guiR    
gui14 --"等待"-->x20{"根据每笔报价收到的报价有效时间区分"}
x20-->x21["等待超过报价有效时间的一半：小兴兴还没收到您的回复，请尽快确认哦。
回复“done”同意，回复“ref”拒绝。"]
x21-->x22["继续等待至报价有效时间：很抱歉，等待超时，上述报价已失效。"]
x22-->gui18["文本展示：本次询价结束，欢迎下次光临！"]
gui18-->x24["返回量化系统成交状态（0105回执接口传递对应码值）"]    
gui18-->guiR
guiR["文本展示：<br/>
请提供您需要的标的、重量、方向、期限。<br/>
重量最低为100KG，最高为1T。<br/>
期限仅接受ON、TN、SN，非上述标准期限请输入<br/>
近端计息日和远端计息日，输入格式为<br/>
YYYY/MM/DD-YYYY/MM/DD<br/>
请依次输入询价要素（标的、重量、方向、期限）<br/>
且用空格隔开。<br/>    例1：AUX 100KG S/B TN<br/>
例2：AUY 200KG B/S 2025/02/26-2025/03/05<br/>
退出请回复0。<br/>"]
guiR--"收到回复0或等待10秒"-->x31["文本展示：本次询价结束，欢迎下次光临！"]    
guiR-->S
```