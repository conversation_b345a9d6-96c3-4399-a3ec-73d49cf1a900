# 对话控制系统设计文档

## 1. 系统概述

### 1.1 系统目标
设计一个可配置化的对话控制系统，支持多种业务品种的询价流程，包括但不限于债券、贵金属等业务。系统需要具备高度的可配置性，确保新增业务品种时无需修改代码或仅需少量代码修改即可快速上线。

### 1.2 系统架构
```
Ideal平台 → 对话控制系统 → 量化交易系统
```

### 1.3 核心设计原则
- **配置驱动**：业务流程通过配置文件定义，无需硬编码
- **模块化设计**：各功能模块独立，便于扩展和维护
- **统一接口**：标准化的接口设计，支持多种业务品种
- **可扩展性**：支持快速新增业务品种和流程节点

## 2. 系统架构设计

### 2.1 整体架构图
```mermaid
graph TB
    A[Ideal平台] --> B[消息接收模块]
    B --> C[会话管理模块]
    C --> D[业务路由模块]
    D --> E[流程引擎模块]
    E --> F[配置管理模块]
    E --> G[消息处理模块]
    G --> H[量化系统接口模块]
    G --> I[消息发送模块]
    I --> A
    H --> J[量化交易系统]
    F --> K[配置数据库]
    C --> L[会话数据库]
    E --> M[日志审计模块]
```

### 2.2 核心模块说明

#### 2.2.1 消息接收模块 (MessageReceiver)
- 负责接收来自Ideal平台的消息
- 消息格式标准化处理
- 消息路由到会话管理模块

#### 2.2.2 会话管理模块 (SessionManager)
- 维护用户会话状态
- 会话超时管理
- 会话数据持久化

#### 2.2.3 业务路由模块 (BusinessRouter)
- 根据用户选择路由到对应业务流程
- 支持多业务品种并发处理
- 业务品种动态注册

#### 2.2.4 流程引擎模块 (ProcessEngine)
- 核心流程控制引擎
- 基于配置驱动的流程执行
- 支持条件分支、循环、异常处理

#### 2.2.5 配置管理模块 (ConfigManager)
- 业务流程配置管理
- 消息模板配置
- 业务规则配置
- 热更新支持

#### 2.2.6 消息处理模块 (MessageProcessor)
- 消息内容解析和验证
- 业务规则校验
- 错误处理和重试机制

#### 2.2.7 量化系统接口模块 (QuantInterface)
- 与量化交易系统的接口封装
- 请求响应处理
- 接口异常处理和降级

#### 2.2.8 消息发送模块 (MessageSender)
- 消息格式化和发送
- 发送状态跟踪
- 失败重试机制

#### 2.2.9 日志审计模块 (AuditLogger)
- 完整的操作日志记录
- 业务审计跟踪
- 性能监控

## 3. 配置化设计

### 3.1 业务流程配置结构
```json
{
  "businessType": "bond", // 业务类型：bond(债券), precious_metal(贵金属)
  "serviceName": "债券询价",
  "serviceTime": {
    "start": "09:00",
    "end": "17:00",
    "timezone": "Asia/Shanghai"
  },
  "workflow": {
    "nodes": [
      {
        "id": "welcome",
        "type": "message",
        "content": "嗨！小兴兴恭候多时！目前我可以为您提供如下服务：...",
        "next": "menu_selection"
      },
      {
        "id": "menu_selection",
        "type": "input_validation",
        "validation": {
          "type": "menu",
          "options": ["1", "0"],
          "maxRetries": 3,
          "timeout": 300
        },
        "branches": {
          "1": "inquiry_input",
          "0": "exit",
          "timeout": "exit",
          "max_retries": "exit"
        }
      }
    ]
  }
}
```

### 3.2 消息模板配置
```json
{
  "templates": {
    "welcome_bond": "嗨！小兴兴恭候多时！目前我可以为您提供如下服务：\n1、现券T+1二级询价(服务时间{serviceTime})\n2、质押式回购溶出（服务时间{serviceTime}）\n0、退出\n每次询价只能询一笔哦",
    "welcome_precious_metal": "嗨！小兴兴恭候多时！目前我可以为您提供如下服务：\n1、贵金属掉期（服务时间为{serviceTime}）\n0、退出\n每次询价只能询一笔哦",
    "service_suspended": "小兴兴{businessName}目前暂停服务，请稍候\n请选择相应数字，如需退出，请回复0",
    "inquiry_format_bond": "请按如下格式发送询价\n各要素以空格分隔，例：bid 110209 1000w T+1\n温馨提示：1、方向、券码、量、交割方式为必须哦！\n2、目前支持询价品种：利率债\n3、支持交割方式：T+1"
  }
}
```

### 3.3 业务规则配置
```json
{
  "businessRules": {
    "bond": {
      "inquiryValidation": {
        "requiredFields": ["direction", "bondCode", "amount", "settlement"],
        "amountMultiple": 10000000,
        "supportedSettlement": ["T+1"],
        "supportedBondTypes": ["利率债"]
      },
      "retryLimits": {
        "inputError": 3,
        "unrecognized": 3
      }
    },
    "precious_metal": {
      "inquiryValidation": {
        "requiredFields": ["underlying", "weight", "direction", "tenor"],
        "weightRange": {
          "min": 100,
          "max": 1000,
          "unit": "KG"
        },
        "supportedUnderlying": ["AUX", "AUY"],
        "supportedTenor": ["ON", "TN", "SN"]
      },
      "whitelistCheck": true
    }
  }
}
```

## 4. 核心类设计

### 4.1 流程引擎核心类
```java
public class ProcessEngine {
    private ConfigManager configManager;
    private MessageProcessor messageProcessor;
    private SessionManager sessionManager;
    
    public ProcessResult executeNode(String sessionId, String nodeId, String userInput) {
        // 获取节点配置
        NodeConfig nodeConfig = configManager.getNodeConfig(nodeId);
        
        // 执行节点逻辑
        return executeNodeLogic(sessionId, nodeConfig, userInput);
    }
    
    private ProcessResult executeNodeLogic(String sessionId, NodeConfig config, String input) {
        switch (config.getType()) {
            case "message":
                return handleMessageNode(sessionId, config);
            case "input_validation":
                return handleInputValidationNode(sessionId, config, input);
            case "api_call":
                return handleApiCallNode(sessionId, config, input);
            case "condition":
                return handleConditionNode(sessionId, config, input);
            default:
                throw new UnsupportedOperationException("Unsupported node type: " + config.getType());
        }
    }
}
```

### 4.2 配置管理类
```java
public class ConfigManager {
    private Map<String, BusinessConfig> businessConfigs;
    private Map<String, MessageTemplate> messageTemplates;
    private Map<String, BusinessRule> businessRules;
    
    public BusinessConfig getBusinessConfig(String businessType) {
        return businessConfigs.get(businessType);
    }
    
    public void reloadConfig() {
        // 热更新配置
    }
    
    public NodeConfig getNodeConfig(String nodeId) {
        // 获取节点配置
    }
}
```

### 4.3 会话管理类
```java
public class SessionManager {
    private Map<String, Session> activeSessions;
    private SessionRepository sessionRepository;
    
    public Session getSession(String sessionId) {
        return activeSessions.computeIfAbsent(sessionId, this::createNewSession);
    }
    
    public void updateSessionState(String sessionId, String currentNode, Map<String, Object> context) {
        Session session = getSession(sessionId);
        session.setCurrentNode(currentNode);
        session.setContext(context);
        sessionRepository.save(session);
    }
}
```

## 5. 数据库设计

### 5.1 会话表 (sessions)
```sql
CREATE TABLE sessions (
    session_id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL,
    business_type VARCHAR(32) NOT NULL,
    current_node VARCHAR(64),
    session_context JSON,
    status VARCHAR(16) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);
```

### 5.2 配置表 (business_configs)
```sql
CREATE TABLE business_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    business_type VARCHAR(32) NOT NULL,
    config_key VARCHAR(128) NOT NULL,
    config_value JSON NOT NULL,
    version INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_business_config (business_type, config_key)
);
```

### 5.3 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(64),
    business_type VARCHAR(32),
    node_id VARCHAR(64),
    action VARCHAR(64),
    input_data JSON,
    output_data JSON,
    execution_time_ms INT,
    status VARCHAR(16),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 6. 接口设计

### 6.1 消息处理接口
```java
@RestController
@RequestMapping("/api/chat")
public class ChatController {
    
    @PostMapping("/message")
    public ResponseEntity<ChatResponse> processMessage(@RequestBody ChatRequest request) {
        // 处理消息逻辑
    }
    
    @PostMapping("/session/start")
    public ResponseEntity<SessionResponse> startSession(@RequestBody SessionRequest request) {
        // 启动会话
    }
    
    @PostMapping("/session/end")
    public ResponseEntity<Void> endSession(@RequestParam String sessionId) {
        // 结束会话
    }
}
```

### 6.2 配置管理接口
```java
@RestController
@RequestMapping("/api/config")
public class ConfigController {
    
    @GetMapping("/business/{businessType}")
    public ResponseEntity<BusinessConfig> getBusinessConfig(@PathVariable String businessType) {
        // 获取业务配置
    }
    
    @PostMapping("/business/{businessType}")
    public ResponseEntity<Void> updateBusinessConfig(
            @PathVariable String businessType, 
            @RequestBody BusinessConfig config) {
        // 更新业务配置
    }
    
    @PostMapping("/reload")
    public ResponseEntity<Void> reloadConfig() {
        // 重新加载配置
    }
}
```

## 7. 新增业务品种流程

### 7.1 配置新业务
1. 在配置数据库中添加新业务类型的配置
2. 定义业务流程节点和分支逻辑
3. 配置消息模板和业务规则
4. 设置量化系统接口映射

### 7.2 测试验证
1. 配置验证：确保配置格式正确
2. 流程测试：验证业务流程完整性
3. 接口测试：验证量化系统接口调用
4. 端到端测试：完整业务流程测试

### 7.3 上线部署
1. 配置热更新：无需重启系统
2. 灰度发布：逐步开放新业务
3. 监控告警：实时监控业务运行状态

## 8. 监控和运维

### 8.1 监控指标
- 会话成功率
- 平均响应时间
- 业务转化率
- 系统错误率
- 量化接口调用成功率

### 8.2 告警机制
- 系统异常告警
- 业务流程异常告警
- 性能指标异常告警
- 配置变更告警

### 8.3 日志管理
- 结构化日志记录
- 日志分级管理
- 日志归档和清理
- 日志查询和分析

## 9. 扩展性考虑

### 9.1 水平扩展
- 无状态服务设计
- 会话数据外部存储
- 负载均衡支持

### 9.2 功能扩展
- 插件化架构支持
- 自定义节点类型
- 第三方系统集成
- 多语言支持

### 9.3 性能优化
- 配置缓存机制
- 异步处理支持
- 连接池管理
- 资源监控和调优

## 10. 总结

本设计文档提供了一个高度可配置的对话控制系统架构，通过配置驱动的方式支持多种业务品种的快速接入。系统具备良好的扩展性和维护性，能够满足未来业务发展的需求。
