# 对话控制系统设计文档

## 1. 系统概述

### 1.1 系统目标
设计一个可配置化的对话控制系统，支持多种业务品种的询价流程，包括但不限于债券、贵金属等业务。系统需要具备高度的可配置性，确保新增业务品种时无需修改代码或仅需少量代码修改即可快速上线。

### 1.2 系统架构
```
Ideal平台 → 对话控制系统 → 量化交易系统
```

### 1.3 核心设计原则
- **配置驱动**：业务流程通过配置文件定义，无需硬编码
- **模块化设计**：各功能模块独立，便于扩展和维护
- **统一接口**：标准化的接口设计，支持多种业务品种
- **可扩展性**：支持快速新增业务品种和流程节点

## 2. 系统架构设计

### 2.1 整体架构图
```mermaid
graph TB
    A[Ideal平台] --> B[消息接收模块]
    B --> C[会话管理模块]
    C --> D[业务路由模块]
    D --> E[流程引擎模块]
    E --> F[配置管理模块]
    E --> G[消息处理模块]
    G --> H[量化系统接口模块]
    G --> I[消息发送模块]
    I --> A
    H --> J[量化交易系统]
    F --> K[配置数据库]
    C --> L[会话数据库]
    E --> M[日志审计模块]
```

### 2.2 核心模块说明

#### 2.2.1 消息接收模块 (MessageReceiver)
- 负责接收来自Ideal平台的消息
- 消息格式标准化处理
- 消息路由到会话管理模块

#### 2.2.2 会话管理模块 (SessionManager)
- 维护用户会话状态
- 会话超时管理
- 会话数据持久化

#### 2.2.3 业务路由模块 (BusinessRouter)
- 根据用户选择路由到对应业务流程
- 支持多业务品种并发处理
- 业务品种动态注册

#### 2.2.4 流程引擎模块 (ProcessEngine)
- 核心流程控制引擎
- 基于配置驱动的流程执行
- 支持条件分支、循环、异常处理

#### 2.2.5 配置管理模块 (ConfigManager)
- 业务流程配置管理
- 消息模板配置
- 业务规则配置
- 热更新支持

#### 2.2.6 消息处理模块 (MessageProcessor)
- 消息内容解析和验证
- 业务规则校验
- 错误处理和重试机制

#### 2.2.7 量化系统接口模块 (QuantInterface)
- 与量化交易系统的接口封装
- 请求响应处理
- 接口异常处理和降级

#### 2.2.8 消息发送模块 (MessageSender)
- 消息格式化和发送
- 发送状态跟踪
- 失败重试机制

#### 2.2.9 日志审计模块 (AuditLogger)
- 完整的操作日志记录
- 业务审计跟踪
- 性能监控

## 3. 配置化设计

### 3.1 业务流程配置结构
```json
{
  "businessType": "bond", // 业务类型：bond(债券), precious_metal(贵金属)
  "serviceName": "债券询价",
  "serviceTime": {
    "start": "09:00",
    "end": "17:00",
    "timezone": "Asia/Shanghai"
  },
  "workflow": {
    "nodes": [
      {
        "id": "welcome",
        "type": "message",
        "content": "嗨！小兴兴恭候多时！目前我可以为您提供如下服务：...",
        "next": "menu_selection"
      },
      {
        "id": "menu_selection",
        "type": "input_validation",
        "validation": {
          "type": "menu",
          "options": ["1", "0"],
          "maxRetries": 3,
          "timeout": 300
        },
        "branches": {
          "1": "inquiry_input",
          "0": "exit",
          "timeout": "exit",
          "max_retries": "exit"
        }
      }
    ]
  }
}
```

### 3.2 消息模板配置
```json
{
  "templates": {
    "welcome_bond": "嗨！小兴兴恭候多时！目前我可以为您提供如下服务：\n1、现券T+1二级询价(服务时间{serviceTime})\n2、质押式回购溶出（服务时间{serviceTime}）\n0、退出\n每次询价只能询一笔哦",
    "welcome_precious_metal": "嗨！小兴兴恭候多时！目前我可以为您提供如下服务：\n1、贵金属掉期（服务时间为{serviceTime}）\n0、退出\n每次询价只能询一笔哦",
    "service_suspended": "小兴兴{businessName}目前暂停服务，请稍候\n请选择相应数字，如需退出，请回复0",
    "inquiry_format_bond": "请按如下格式发送询价\n各要素以空格分隔，例：bid 110209 1000w T+1\n温馨提示：1、方向、券码、量、交割方式为必须哦！\n2、目前支持询价品种：利率债\n3、支持交割方式：T+1"
  }
}
```

### 3.3 业务规则配置
```json
{
  "businessRules": {
    "bond": {
      "inquiryValidation": {
        "requiredFields": ["direction", "bondCode", "amount", "settlement"],
        "amountMultiple": 10000000,
        "supportedSettlement": ["T+1"],
        "supportedBondTypes": ["利率债"]
      },
      "retryLimits": {
        "inputError": 3,
        "unrecognized": 3
      }
    },
    "precious_metal": {
      "inquiryValidation": {
        "requiredFields": ["underlying", "weight", "direction", "tenor"],
        "weightRange": {
          "min": 100,
          "max": 1000,
          "unit": "KG"
        },
        "supportedUnderlying": ["AUX", "AUY"],
        "supportedTenor": ["ON", "TN", "SN"]
      },
      "whitelistCheck": true
    }
  }
}
```

## 4. 核心类设计

### 4.1 流程引擎核心类
```java
public class ProcessEngine {
    private ConfigManager configManager;
    private MessageProcessor messageProcessor;
    private SessionManager sessionManager;
    
    public ProcessResult executeNode(String sessionId, String nodeId, String userInput) {
        // 获取节点配置
        NodeConfig nodeConfig = configManager.getNodeConfig(nodeId);
        
        // 执行节点逻辑
        return executeNodeLogic(sessionId, nodeConfig, userInput);
    }
    
    private ProcessResult executeNodeLogic(String sessionId, NodeConfig config, String input) {
        switch (config.getType()) {
            case "message":
                return handleMessageNode(sessionId, config);
            case "input_validation":
                return handleInputValidationNode(sessionId, config, input);
            case "api_call":
                return handleApiCallNode(sessionId, config, input);
            case "condition":
                return handleConditionNode(sessionId, config, input);
            default:
                throw new UnsupportedOperationException("Unsupported node type: " + config.getType());
        }
    }
}
```

### 4.2 配置管理类
```java
public class ConfigManager {
    private Map<String, BusinessConfig> businessConfigs;
    private Map<String, MessageTemplate> messageTemplates;
    private Map<String, BusinessRule> businessRules;
    
    public BusinessConfig getBusinessConfig(String businessType) {
        return businessConfigs.get(businessType);
    }
    
    public void reloadConfig() {
        // 热更新配置
    }
    
    public NodeConfig getNodeConfig(String nodeId) {
        // 获取节点配置
    }
}
```

### 4.3 会话管理类
```java
public class SessionManager {
    private Map<String, Session> activeSessions;
    private SessionRepository sessionRepository;
    
    public Session getSession(String sessionId) {
        return activeSessions.computeIfAbsent(sessionId, this::createNewSession);
    }
    
    public void updateSessionState(String sessionId, String currentNode, Map<String, Object> context) {
        Session session = getSession(sessionId);
        session.setCurrentNode(currentNode);
        session.setContext(context);
        sessionRepository.save(session);
    }
}
```

## 5. 数据库设计

### 5.1 会话表 (sessions)
```sql
CREATE TABLE sessions (
    session_id VARCHAR(64) PRIMARY KEY,
    user_id VARCHAR(64) NOT NULL,
    business_type VARCHAR(32) NOT NULL,
    current_node VARCHAR(64),
    session_context JSON,
    status VARCHAR(16) DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);
```

### 5.2 配置表 (business_configs)
```sql
CREATE TABLE business_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    business_type VARCHAR(32) NOT NULL,
    config_key VARCHAR(128) NOT NULL,
    config_value JSON NOT NULL,
    version INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_business_config (business_type, config_key)
);
```

### 5.3 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(64),
    business_type VARCHAR(32),
    node_id VARCHAR(64),
    action VARCHAR(64),
    input_data JSON,
    output_data JSON,
    execution_time_ms INT,
    status VARCHAR(16),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 6. 接口设计

### 6.1 消息处理接口
```java
@RestController
@RequestMapping("/api/chat")
public class ChatController {
    
    @PostMapping("/message")
    public ResponseEntity<ChatResponse> processMessage(@RequestBody ChatRequest request) {
        // 处理消息逻辑
    }
    
    @PostMapping("/session/start")
    public ResponseEntity<SessionResponse> startSession(@RequestBody SessionRequest request) {
        // 启动会话
    }
    
    @PostMapping("/session/end")
    public ResponseEntity<Void> endSession(@RequestParam String sessionId) {
        // 结束会话
    }
}
```

### 6.2 配置管理接口
```java
@RestController
@RequestMapping("/api/config")
public class ConfigController {
    
    @GetMapping("/business/{businessType}")
    public ResponseEntity<BusinessConfig> getBusinessConfig(@PathVariable String businessType) {
        // 获取业务配置
    }
    
    @PostMapping("/business/{businessType}")
    public ResponseEntity<Void> updateBusinessConfig(
            @PathVariable String businessType, 
            @RequestBody BusinessConfig config) {
        // 更新业务配置
    }
    
    @PostMapping("/reload")
    public ResponseEntity<Void> reloadConfig() {
        // 重新加载配置
    }
}
```

## 7. 新增业务品种流程

### 7.1 配置新业务
1. 在配置数据库中添加新业务类型的配置
2. 定义业务流程节点和分支逻辑
3. 配置消息模板和业务规则
4. 设置量化系统接口映射

### 7.2 测试验证
1. 配置验证：确保配置格式正确
2. 流程测试：验证业务流程完整性
3. 接口测试：验证量化系统接口调用
4. 端到端测试：完整业务流程测试

### 7.3 上线部署
1. 配置热更新：无需重启系统
2. 灰度发布：逐步开放新业务
3. 监控告警：实时监控业务运行状态

## 8. 监控和运维

### 8.1 监控指标
- 会话成功率
- 平均响应时间
- 业务转化率
- 系统错误率
- 量化接口调用成功率

### 8.2 告警机制
- 系统异常告警
- 业务流程异常告警
- 性能指标异常告警
- 配置变更告警

### 8.3 日志管理
- 结构化日志记录
- 日志分级管理
- 日志归档和清理
- 日志查询和分析

## 9. 扩展性考虑

### 9.1 水平扩展
- 无状态服务设计
- 会话数据外部存储
- 负载均衡支持

### 9.2 功能扩展
- 插件化架构支持
- 自定义节点类型
- 第三方系统集成
- 多语言支持

### 9.3 性能优化
- 配置缓存机制
- 异步处理支持
- 连接池管理
- 资源监控和调优

## 10. 详细模块功能设计

### 10.1 消息接收模块 (MessageReceiver)

#### 10.1.1 接收Ideal消息功能
**输入：**
- 消息体：包含用户ID、消息内容、时间戳、消息类型
- HTTP请求头：包含认证信息、请求ID

**处理逻辑：**
1. 验证请求合法性（签名验证、IP白名单）
2. 解析消息格式，提取关键信息
3. 消息去重处理（基于消息ID）
4. 消息格式标准化转换
5. 记录接收日志

**输出：**
- 标准化消息对象：StandardMessage
- 处理结果：成功/失败状态码

#### 10.1.2 消息格式验证功能
**输入：**
- 原始消息数据

**处理逻辑：**
1. JSON格式校验
2. 必填字段检查（userId, content, timestamp）
3. 字段类型和长度验证
4. 特殊字符过滤和转义

**输出：**
- 验证结果：ValidationResult
- 错误信息列表

#### 10.1.3 消息路由功能
**输入：**
- 标准化消息对象

**处理逻辑：**
1. 根据用户ID获取或创建会话
2. 判断消息类型（文本/图片/文件等）
3. 路由到对应的处理模块
4. 异步处理支持

**输出：**
- 路由结果：成功转发到会话管理模块

### 10.2 会话管理模块 (SessionManager)

#### 10.2.1 会话创建功能
**输入：**
- 用户ID
- 业务类型（可选，默认为通用）
- 初始上下文信息

**处理逻辑：**
1. 生成唯一会话ID（UUID）
2. 初始化会话状态
3. 设置会话过期时间
4. 保存到会话存储（Redis + MySQL）
5. 记录会话创建日志

**输出：**
- 会话对象：Session
- 会话ID

#### 10.2.2 会话状态管理功能
**输入：**
- 会话ID
- 状态更新信息（当前节点、上下文数据）

**处理逻辑：**
1. 验证会话是否存在且有效
2. 更新会话状态信息
3. 更新最后活跃时间
4. 同步到持久化存储
5. 触发状态变更事件

**输出：**
- 更新结果：成功/失败
- 更新后的会话对象

#### 10.2.3 会话超时管理功能
**输入：**
- 定时任务触发

**处理逻辑：**
1. 扫描过期会话（定时任务，每分钟执行）
2. 发送超时提醒消息
3. 清理过期会话数据
4. 记录超时统计信息

**输出：**
- 清理的会话数量
- 超时统计报告

#### 10.2.4 会话上下文管理功能
**输入：**
- 会话ID
- 上下文键值对

**处理逻辑：**
1. 获取当前会话上下文
2. 合并新的上下文数据
3. 验证上下文数据大小限制
4. 更新会话存储

**输出：**
- 更新后的完整上下文

### 10.3 业务路由模块 (BusinessRouter)

#### 10.3.1 业务类型识别功能
**输入：**
- 用户消息内容
- 会话历史信息

**处理逻辑：**
1. 基于关键词匹配识别业务类型
2. 结合会话上下文判断
3. 支持模糊匹配和智能推荐
4. 处理多业务场景冲突

**输出：**
- 识别的业务类型
- 置信度分数

#### 10.3.2 业务流程路由功能
**输入：**
- 业务类型
- 当前会话状态

**处理逻辑：**
1. 获取业务流程配置
2. 确定当前流程节点
3. 计算下一个执行节点
4. 验证流程权限

**输出：**
- 目标流程节点ID
- 路由参数

#### 10.3.3 负载均衡功能
**输入：**
- 业务请求

**处理逻辑：**
1. 监控各业务处理器负载
2. 基于轮询/权重算法分配
3. 处理器健康检查
4. 故障转移机制

**输出：**
- 选中的处理器实例
- 负载分配结果

### 10.4 流程引擎模块 (ProcessEngine)

#### 10.4.1 节点执行功能
**输入：**
- 会话ID
- 节点ID
- 用户输入内容

**处理逻辑：**
1. 获取节点配置信息
2. 验证节点执行条件
3. 执行节点特定逻辑
4. 处理节点执行结果
5. 计算下一个节点

**输出：**
- 节点执行结果
- 下一个节点ID
- 输出消息内容

#### 10.4.2 条件判断功能
**输入：**
- 条件表达式
- 上下文变量

**处理逻辑：**
1. 解析条件表达式
2. 获取相关变量值
3. 执行条件计算
4. 返回布尔结果

**输出：**
- 条件判断结果（true/false）

#### 10.4.3 流程分支控制功能
**输入：**
- 分支条件配置
- 当前执行上下文

**处理逻辑：**
1. 评估所有分支条件
2. 选择匹配的分支路径
3. 处理默认分支逻辑
4. 记录分支选择日志

**输出：**
- 选中的分支路径
- 分支参数

#### 10.4.4 异常处理功能
**输入：**
- 异常信息
- 当前执行上下文

**处理逻辑：**
1. 异常类型识别和分类
2. 查找异常处理策略
3. 执行恢复或降级逻辑
4. 记录异常日志

**输出：**
- 异常处理结果
- 恢复后的执行路径

### 10.5 配置管理模块 (ConfigManager)

#### 10.5.1 配置加载功能
**输入：**
- 配置文件路径或数据库连接

**处理逻辑：**
1. 从多个数据源加载配置
2. 配置格式验证和解析
3. 配置版本管理
4. 配置缓存机制

**输出：**
- 配置对象集合
- 加载状态报告

#### 10.5.2 配置热更新功能
**输入：**
- 配置变更通知

**处理逻辑：**
1. 监听配置变更事件
2. 增量更新配置缓存
3. 通知相关模块重新加载
4. 验证配置有效性

**输出：**
- 更新结果状态
- 影响的模块列表

#### 10.5.3 配置版本管理功能
**输入：**
- 配置更新请求

**处理逻辑：**
1. 创建配置版本快照
2. 记录变更历史
3. 支持配置回滚
4. 版本比较和差异分析

**输出：**
- 新版本号
- 变更摘要

### 10.6 消息处理模块 (MessageProcessor)

#### 10.6.1 消息解析功能
**输入：**
- 用户原始消息

**处理逻辑：**
1. 文本内容提取和清理
2. 关键信息识别（数字、日期、代码等）
3. 意图识别和实体抽取
4. 格式化处理

**输出：**
- 解析后的结构化数据
- 识别的实体列表

#### 10.6.2 业务规则验证功能
**输入：**
- 解析后的消息数据
- 业务规则配置

**处理逻辑：**
1. 应用业务规则进行验证
2. 字段完整性检查
3. 数据格式和范围验证
4. 业务逻辑一致性检查

**输出：**
- 验证结果
- 错误信息列表

#### 10.6.3 消息模板渲染功能
**输入：**
- 模板ID
- 模板参数

**处理逻辑：**
1. 获取消息模板
2. 参数替换和渲染
3. 多语言支持
4. 格式化输出

**输出：**
- 渲染后的消息内容

### 10.7 量化系统接口模块 (QuantInterface)

#### 10.7.1 询价请求功能
**输入：**
- 询价参数（券码、数量、方向等）

**处理逻辑：**
1. 参数格式转换
2. 构建API请求
3. 发送HTTP请求
4. 响应解析和验证

**输出：**
- 询价响应结果
- 接口调用状态

#### 10.7.2 报价结果处理功能
**输入：**
- 量化系统报价响应

**处理逻辑：**
1. 响应数据解析
2. 报价有效性验证
3. 格式化报价信息
4. 异常情况处理

**输出：**
- 标准化报价对象
- 处理状态

#### 10.7.3 成交回执功能
**输入：**
- 成交确认信息

**处理逻辑：**
1. 构建回执请求
2. 发送成交确认
3. 处理回执响应
4. 状态同步

**输出：**
- 回执处理结果

### 10.8 消息发送模块 (MessageSender)

#### 10.8.1 消息格式化功能
**输入：**
- 消息内容
- 目标用户信息

**处理逻辑：**
1. 消息格式转换
2. 特殊字符处理
3. 长度限制检查
4. 多媒体内容处理

**输出：**
- 格式化后的消息

#### 10.8.2 消息发送功能
**输入：**
- 格式化消息
- 发送参数

**处理逻辑：**
1. 选择发送通道
2. 构建发送请求
3. 执行消息发送
4. 发送状态跟踪

**输出：**
- 发送结果状态
- 消息ID

#### 10.8.3 发送失败重试功能
**输入：**
- 失败的消息发送任务

**处理逻辑：**
1. 分析失败原因
2. 计算重试间隔
3. 执行重试逻辑
4. 达到最大重试次数后放弃

**输出：**
- 重试结果
- 最终状态

### 10.9 日志审计模块 (AuditLogger)

#### 10.9.1 操作日志记录功能
**输入：**
- 操作事件信息

**处理逻辑：**
1. 日志格式标准化
2. 敏感信息脱敏
3. 日志级别分类
4. 异步写入存储

**输出：**
- 日志记录ID

#### 10.9.2 性能监控功能
**输入：**
- 性能指标数据

**处理逻辑：**
1. 指标数据收集
2. 统计计算
3. 阈值监控
4. 告警触发

**输出：**
- 性能报告
- 告警信息

#### 10.9.3 审计报告生成功能
**输入：**
- 报告查询条件

**处理逻辑：**
1. 数据查询和聚合
2. 报告模板应用
3. 图表生成
4. 报告导出

**输出：**
- 审计报告文件

## 11. 数据库ER图设计

### 11.1 核心实体关系图

```mermaid
erDiagram
    USERS {
        varchar user_id PK
        varchar user_name
        varchar institution
        varchar contact_info
        varchar status
        timestamp created_at
        timestamp updated_at
    }

    SESSIONS {
        varchar session_id PK
        varchar user_id FK
        varchar business_type
        varchar current_node
        json session_context
        varchar status
        timestamp created_at
        timestamp updated_at
        timestamp expires_at
    }

    BUSINESS_CONFIGS {
        bigint id PK
        varchar business_type
        varchar config_key
        json config_value
        int version
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    MESSAGE_TEMPLATES {
        bigint id PK
        varchar business_type
        varchar template_key
        text template_content
        varchar language
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    BUSINESS_RULES {
        bigint id PK
        varchar business_type
        varchar rule_key
        json rule_config
        int priority
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    AUDIT_LOGS {
        bigint id PK
        varchar session_id FK
        varchar business_type
        varchar node_id
        varchar action
        json input_data
        json output_data
        int execution_time_ms
        varchar status
        text error_message
        timestamp created_at
    }

    QUANT_REQUESTS {
        bigint id PK
        varchar session_id FK
        varchar request_id
        varchar business_type
        json request_params
        json response_data
        varchar status
        int response_time_ms
        timestamp created_at
        timestamp updated_at
    }

    TRANSACTION_RECORDS {
        bigint id PK
        varchar session_id FK
        varchar transaction_id
        varchar business_type
        varchar bond_code
        decimal amount
        varchar direction
        varchar settlement_type
        varchar status
        varchar trader_info
        timestamp created_at
        timestamp updated_at
    }

    WHITELIST_CONFIGS {
        bigint id PK
        varchar business_type
        varchar institution_id
        varchar user_id
        boolean is_active
        timestamp valid_from
        timestamp valid_to
        timestamp created_at
        timestamp updated_at
    }

    SYSTEM_CONFIGS {
        bigint id PK
        varchar config_group
        varchar config_key
        varchar config_value
        varchar description
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    USERS ||--o{ SESSIONS : "has"
    SESSIONS ||--o{ AUDIT_LOGS : "generates"
    SESSIONS ||--o{ QUANT_REQUESTS : "makes"
    SESSIONS ||--o{ TRANSACTION_RECORDS : "creates"
    BUSINESS_CONFIGS ||--o{ SESSIONS : "configures"
    MESSAGE_TEMPLATES ||--o{ AUDIT_LOGS : "uses"
    BUSINESS_RULES ||--o{ AUDIT_LOGS : "applies"
    WHITELIST_CONFIGS ||--o{ USERS : "controls"
```

### 11.2 详细表结构设计

#### 11.2.1 用户表 (users)
```sql
CREATE TABLE users (
    user_id VARCHAR(64) PRIMARY KEY COMMENT '用户唯一标识',
    user_name VARCHAR(128) NOT NULL COMMENT '用户姓名',
    institution VARCHAR(128) NOT NULL COMMENT '所属机构',
    contact_info JSON COMMENT '联系方式信息',
    status VARCHAR(16) DEFAULT 'ACTIVE' COMMENT '用户状态：ACTIVE/INACTIVE/BLOCKED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_institution (institution),
    INDEX idx_status (status)
) COMMENT '用户基础信息表';
```

#### 11.2.2 会话表 (sessions)
```sql
CREATE TABLE sessions (
    session_id VARCHAR(64) PRIMARY KEY COMMENT '会话唯一标识',
    user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    current_node VARCHAR(64) COMMENT '当前流程节点',
    session_context JSON COMMENT '会话上下文数据',
    status VARCHAR(16) DEFAULT 'ACTIVE' COMMENT '会话状态：ACTIVE/COMPLETED/EXPIRED/CANCELLED',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    expires_at TIMESTAMP COMMENT '过期时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_user_business (user_id, business_type),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) COMMENT '用户会话信息表';
```

#### 11.2.3 业务配置表 (business_configs)
```sql
CREATE TABLE business_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    config_key VARCHAR(128) NOT NULL COMMENT '配置键',
    config_value JSON NOT NULL COMMENT '配置值',
    version INT DEFAULT 1 COMMENT '配置版本',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_business_config (business_type, config_key),
    INDEX idx_business_type (business_type),
    INDEX idx_is_active (is_active)
) COMMENT '业务配置表';
```

#### 11.2.4 消息模板表 (message_templates)
```sql
CREATE TABLE message_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    template_key VARCHAR(128) NOT NULL COMMENT '模板键',
    template_content TEXT NOT NULL COMMENT '模板内容',
    language VARCHAR(8) DEFAULT 'zh-CN' COMMENT '语言',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_template (business_type, template_key, language),
    INDEX idx_business_type (business_type),
    INDEX idx_is_active (is_active)
) COMMENT '消息模板表';
```

#### 11.2.5 业务规则表 (business_rules)
```sql
CREATE TABLE business_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    rule_key VARCHAR(128) NOT NULL COMMENT '规则键',
    rule_config JSON NOT NULL COMMENT '规则配置',
    priority INT DEFAULT 0 COMMENT '优先级',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_business_rule (business_type, rule_key),
    INDEX idx_business_type (business_type),
    INDEX idx_priority (priority),
    INDEX idx_is_active (is_active)
) COMMENT '业务规则表';
```

#### 11.2.6 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(64) COMMENT '会话ID',
    business_type VARCHAR(32) COMMENT '业务类型',
    node_id VARCHAR(64) COMMENT '流程节点ID',
    action VARCHAR(64) NOT NULL COMMENT '操作动作',
    input_data JSON COMMENT '输入数据',
    output_data JSON COMMENT '输出数据',
    execution_time_ms INT COMMENT '执行时间(毫秒)',
    status VARCHAR(16) NOT NULL COMMENT '执行状态：SUCCESS/FAILED/TIMEOUT',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
    INDEX idx_session_id (session_id),
    INDEX idx_business_type (business_type),
    INDEX idx_action (action),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '操作审计日志表';
```

#### 11.2.7 量化请求表 (quant_requests)
```sql
CREATE TABLE quant_requests (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    request_id VARCHAR(64) NOT NULL COMMENT '请求唯一标识',
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    request_params JSON NOT NULL COMMENT '请求参数',
    response_data JSON COMMENT '响应数据',
    status VARCHAR(16) NOT NULL COMMENT '请求状态：PENDING/SUCCESS/FAILED/TIMEOUT',
    response_time_ms INT COMMENT '响应时间(毫秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
    UNIQUE KEY uk_request_id (request_id),
    INDEX idx_session_id (session_id),
    INDEX idx_business_type (business_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '量化系统请求记录表';
```

#### 11.2.8 交易记录表 (transaction_records)
```sql
CREATE TABLE transaction_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    transaction_id VARCHAR(64) NOT NULL COMMENT '交易唯一标识',
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    bond_code VARCHAR(32) COMMENT '债券代码',
    amount DECIMAL(15,2) COMMENT '交易金额',
    direction VARCHAR(8) COMMENT '交易方向：BID/OFR',
    settlement_type VARCHAR(16) COMMENT '交割方式',
    status VARCHAR(16) NOT NULL COMMENT '交易状态：QUOTED/CONFIRMED/REJECTED/EXPIRED',
    trader_info JSON COMMENT '交易员信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (session_id) REFERENCES sessions(session_id),
    UNIQUE KEY uk_transaction_id (transaction_id),
    INDEX idx_session_id (session_id),
    INDEX idx_business_type (business_type),
    INDEX idx_bond_code (bond_code),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) COMMENT '交易记录表';
```

#### 11.2.9 白名单配置表 (whitelist_configs)
```sql
CREATE TABLE whitelist_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    business_type VARCHAR(32) NOT NULL COMMENT '业务类型',
    institution_id VARCHAR(64) COMMENT '机构ID',
    user_id VARCHAR(64) COMMENT '用户ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生效时间',
    valid_to TIMESTAMP COMMENT '失效时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_business_type (business_type),
    INDEX idx_institution_id (institution_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_valid_period (valid_from, valid_to)
) COMMENT '业务白名单配置表';
```

#### 11.2.10 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    config_group VARCHAR(64) NOT NULL COMMENT '配置分组',
    config_key VARCHAR(128) NOT NULL COMMENT '配置键',
    config_value VARCHAR(512) NOT NULL COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_group_key (config_group, config_key),
    INDEX idx_config_group (config_group),
    INDEX idx_is_active (is_active)
) COMMENT '系统配置表';
```

## 12. 模块调用泳道图

### 12.1 完整询价流程泳道图

```mermaid
sequenceDiagram
    participant U as 用户(Ideal)
    participant MR as 消息接收模块
    participant SM as 会话管理模块
    participant BR as 业务路由模块
    participant PE as 流程引擎模块
    participant CM as 配置管理模块
    participant MP as 消息处理模块
    participant QI as 量化接口模块
    participant MS as 消息发送模块
    participant AL as 日志审计模块

    U->>MR: 发送消息
    MR->>AL: 记录接收日志
    MR->>MR: 消息格式验证
    MR->>SM: 获取/创建会话
    SM->>SM: 检查会话状态
    SM->>BR: 路由到业务模块
    BR->>CM: 获取业务配置
    CM-->>BR: 返回配置信息
    BR->>PE: 启动流程引擎

    loop 流程节点执行
        PE->>CM: 获取节点配置
        CM-->>PE: 返回节点配置
        PE->>MP: 处理用户输入
        MP->>MP: 消息解析和验证
        MP-->>PE: 返回处理结果

        alt 需要调用量化系统
            PE->>QI: 发起量化请求
            QI->>QI: 构建API请求
            QI-->>PE: 返回量化响应
        end

        PE->>SM: 更新会话状态
        PE->>MS: 发送响应消息
        MS->>U: 返回消息
        PE->>AL: 记录执行日志
    end

    PE->>SM: 更新最终状态
    SM->>AL: 记录会话结束
```

### 12.2 债券询价具体流程泳道图

```mermaid
sequenceDiagram
    participant U as 用户
    participant PE as 流程引擎
    participant MP as 消息处理模块
    participant QI as 量化接口模块
    participant MS as 消息发送模块
    participant AL as 日志审计模块

    U->>PE: 选择债券询价(1)
    PE->>MS: 发送询价格式说明
    MS->>U: 显示格式要求

    U->>PE: 输入询价要素
    PE->>MP: 解析询价要素
    MP->>MP: 验证要素完整性

    alt 要素不完整
        MP-->>PE: 返回验证失败
        PE->>MS: 发送错误提示
        MS->>U: 显示缺失要素
    else 要素完整
        MP-->>PE: 返回验证成功
        PE->>QI: 调用量化询价接口
        QI->>QI: 构建询价请求

        alt 量化系统异常
            QI-->>PE: 返回接口异常
            PE->>MS: 发送系统异常消息
            MS->>U: 显示异常提示
            PE->>AL: 记录异常日志
        else 量化系统正常
            QI-->>PE: 返回询价结果

            alt 拒绝询价
                PE->>MS: 发送拒绝原因
                MS->>U: 显示拒绝信息
                PE->>AL: 记录拒绝状态
            else 成功报价
                PE->>MS: 发送报价信息
                MS->>U: 显示报价详情

                U->>PE: 回复done/ref
                PE->>MP: 解析用户回复

                alt 回复done
                    MP-->>PE: 确认成交
                    PE->>MS: 发送交易方式选择
                    MS->>U: 显示交易方式菜单

                    U->>PE: 选择交易方式
                    PE->>QI: 发送成交回执
                    QI-->>PE: 返回回执结果
                    PE->>MS: 发送成交确认
                    MS->>U: 显示成交信息
                    PE->>AL: 记录成交日志
                else 回复ref
                    MP-->>PE: 拒绝成交
                    PE->>QI: 发送拒绝回执
                    PE->>MS: 发送结束消息
                    MS->>U: 显示询价结束
                    PE->>AL: 记录拒绝日志
                end
            end
        end
    end
```

### 12.3 贵金属询价具体流程泳道图

```mermaid
sequenceDiagram
    participant U as 用户
    participant PE as 流程引擎
    participant MP as 消息处理模块
    participant QI as 量化接口模块
    participant MS as 消息发送模块
    participant WL as 白名单检查

    U->>PE: 选择贵金属询价(1)
    PE->>WL: 检查用户白名单

    alt 不在白名单
        WL-->>PE: 返回未授权
        PE->>MS: 发送无权限消息
        MS->>U: 显示无交易额度
    else 在白名单
        WL-->>PE: 返回授权通过
        PE->>MS: 发送询价格式说明
        MS->>U: 显示格式要求

        U->>PE: 输入询价要素
        PE->>MP: 解析询价要素
        MP->>MP: 验证要素和重量范围

        alt 要素不完整或重量不符
            MP-->>PE: 返回验证失败
            PE->>MS: 发送错误提示
            MS->>U: 显示具体错误
        else 要素完整且重量符合
            MP-->>PE: 返回验证成功
            PE->>QI: 调用量化询价接口

            alt 量化系统拒绝
                QI-->>PE: 返回拒绝结果
                PE->>MS: 发送拒绝消息
                MS->>U: 显示拒绝原因
            else 量化系统报价
                QI-->>PE: 返回报价结果
                PE->>MS: 发送报价信息
                MS->>U: 显示报价详情

                U->>PE: 回复done/ref
                PE->>MP: 解析用户回复

                alt 回复done
                    MP-->>PE: 确认成交
                    PE->>QI: 发送成交回执
                    PE->>MS: 发送成交指引
                    MS->>U: 显示RFQ发起指引
                else 回复ref
                    MP-->>PE: 拒绝成交
                    PE->>QI: 发送拒绝回执
                    PE->>MS: 发送结束消息
                    MS->>U: 显示询价结束
                end
            end
        end
    end
```

### 12.4 配置热更新流程泳道图

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant CM as 配置管理模块
    participant DB as 配置数据库
    participant Cache as 配置缓存
    participant PE as 流程引擎
    participant BR as 业务路由模块
    participant AL as 日志审计模块

    Admin->>CM: 提交配置更新
    CM->>CM: 验证配置格式

    alt 配置格式错误
        CM-->>Admin: 返回验证失败
    else 配置格式正确
        CM->>DB: 保存新配置版本
        DB-->>CM: 返回保存结果
        CM->>Cache: 更新配置缓存
        Cache-->>CM: 返回更新结果

        CM->>PE: 通知配置变更
        CM->>BR: 通知配置变更
        PE->>CM: 重新加载流程配置
        BR->>CM: 重新加载路由配置

        CM->>AL: 记录配置变更日志
        CM-->>Admin: 返回更新成功
    end
```

### 12.5 异常处理流程泳道图

```mermaid
sequenceDiagram
    participant PE as 流程引擎
    participant QI as 量化接口模块
    participant MS as 消息发送模块
    participant AL as 日志审计模块
    participant Monitor as 监控系统

    PE->>QI: 调用量化接口
    QI->>QI: 发送HTTP请求

    alt 网络超时
        QI-->>PE: 返回超时异常
        PE->>AL: 记录超时日志
        PE->>Monitor: 发送告警
        PE->>MS: 发送超时提示消息
    else 接口返回错误
        QI-->>PE: 返回业务异常
        PE->>AL: 记录业务异常
        PE->>MS: 发送业务错误消息
    else 系统内部错误
        QI-->>PE: 返回系统异常
        PE->>AL: 记录系统异常
        PE->>Monitor: 发送严重告警
        PE->>MS: 发送系统维护消息
    end
```

## 13. 总结

本设计文档提供了一个高度可配置的对话控制系统架构，通过配置驱动的方式支持多种业务品种的快速接入。系统具备良好的扩展性和维护性，能够满足未来业务发展的需求。

### 13.1 设计亮点
1. **模块化设计**：各模块职责清晰，便于独立开发和测试
2. **配置驱动**：业务流程完全配置化，支持快速业务上线
3. **数据完整性**：完善的数据库设计和约束关系
4. **可观测性**：全面的日志审计和监控机制
5. **高可用性**：异常处理和降级机制完善

### 13.2 技术优势
1. **零代码新增业务**：通过配置即可新增业务品种
2. **热更新支持**：配置变更无需重启系统
3. **标准化接口**：统一的模块间调用接口
4. **完整的数据追踪**：从用户输入到最终结果的全链路追踪
